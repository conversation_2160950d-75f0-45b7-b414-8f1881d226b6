# =============================================================================
# IZY MERCADO BACKEND - ENVIRONMENT CONFIGURATION EXAMPLE
# =============================================================================
# Copy this file to .env and fill in your actual values

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================
APP_ENV=development
SWAGGER_HOST=localhost:8080
BASIC_AUTH_PASSWORD=change_me
JWT_SECRET=your_super_secret_jwt_key_here

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Local development
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/izy-mercado-db?sslmode=disable

# Production with SSL (values come from Google Cloud Secret Manager)
# DATABASE_URL=*****************************************************/izy-mercado-db

# SSL Configuration (for Cloud SQL production)
# DATABASE_SSL_SERVER_CA=-----BEGIN CERTIFICATE-----...-----END CERTIFICATE-----
# DATABASE_SSL_CLIENT_CERT=-----BEGIN CERTIFICATE-----...-----END CERTIFICATE-----
# DATABASE_SSL_CLIENT_KEY=-----BEGIN PRIVATE KEY-----...-----END PRIVATE KEY-----

# =============================================================================
# STORAGE CONFIGURATION (Cloudflare R2)
# =============================================================================
STORAGE_BUCKET_NAME=your-bucket-name
STORAGE_ACCOUNT_ID=your-cloudflare-account-id
STORAGE_ACCESS_KEY_ID=your-r2-access-key-id
STORAGE_ACCESS_KEY_SECRET=your-r2-secret-access-key

# =============================================================================
# MAILER CONFIGURATION
# =============================================================================
MAILER_FROM=<EMAIL>
MAILER_TOKEN=your-mailer-api-token
MAILER_URL=https://api.your-mailer-service.com
MAILER_SEND_CODE_TEMPLATE_KEY=your-template-key

# =============================================================================
# PAYMENT INTEGRATION (WOOVI)
# =============================================================================
WOOVI_API_KEY=your-woovi-api-key
WOOVI_URL=https://api.woovi.com
WOOVI_PIX_KEY=your-pix-key

# =============================================================================
# MONITORING (NEW RELIC)
# =============================================================================
NEW_RELIC_LICENSE_KEY=your-new-relic-license-key
NEW_RELIC_APP_NAME=izy-api-dev

# =============================================================================
# FIREBASE CLOUD MESSAGING (FCM)
# =============================================================================
# For local development
FCM_SERVICE_ACCOUNT_PATH=./credentials.json

# For production (JSON content)
# FCM_SERVICE_ACCOUNT_JSON={"type":"service_account",...}

FCM_PROJECT_ID=your-firebase-project-id
FCM_VAPID_KEY=your-vapid-key
