package withdrawalprocessor

import (
	"context"
	"os"
	"testing"
	"time"
)

// TestDatabaseConnection tests the database connection with SSL
func TestDatabaseConnection(t *testing.T) {
	// Skip test if no database URL is provided
	if os.<PERSON>env("DATABASE_URL") == "" {
		t.<PERSON><PERSON>("DATABASE_URL not set, skipping database connection test")
	}

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	conn, err := connectToDatabase()
	if err != nil {
		t.Fatalf("Failed to connect to database: %v", err)
	}
	defer conn.Close(ctx)

	// Test ping
	if err := conn.Ping(ctx); err != nil {
		t.Fatalf("Failed to ping database: %v", err)
	}

	t.Log("Database connection successful with SSL")
}
