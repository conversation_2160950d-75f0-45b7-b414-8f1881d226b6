package logger

import (
	"context"
	"io"
	"os"
	"strings"
	"time"

	"github.com/rs/zerolog"
)

// Context<PERSON><PERSON> represents the type for context keys
type ContextKey string

const (
	// OrderIDKey is the context key for OrderID
	OrderIDKey ContextKey = "order_id"
	// UserIDKey is the context key for UserID
	UserExternalIdKey ContextKey = "user_external_id"
	// CompanyIDKey is the context key for CompanyID
	CompanyExternalIdKey ContextKey = "company_id"
)

// GCP Cloud Logging severity levels
const (
	SeverityDefault   = "DEFAULT"
	SeverityDebug     = "DEBUG"
	SeverityInfo      = "INFO"
	SeverityNotice    = "NOTICE"
	SeverityWarning   = "WARNING"
	SeverityError     = "ERROR"
	SeverityCritical  = "CRITICAL"
	SeverityAlert     = "ALERT"
	SeverityEmergency = "EMERGENCY"
)

// GCP Cloud Logging special fields
const (
	GCPSeverityField       = "severity"
	GCPMessageField        = "message"
	GCPTimestampField      = "timestamp"
	GCPTraceField          = "logging.googleapis.com/trace"
	GCPSpanIDField         = "logging.googleapis.com/spanId"
	GCPOperationField      = "logging.googleapis.com/operation"
	GCPSourceLocationField = "logging.googleapis.com/sourceLocation"
)

// Logger interface defines the logging contract
// This interface decouples business logic from the specific logging implementation
type Logger interface {
	// Context-aware logging methods
	WithContext(ctx context.Context) LogEntry
	WithOrderID(orderID string) LogEntry
	WithUserExternalID(userExternalID string) LogEntry
	WithCompanyExternalID(companyExternalID string) LogEntry
	WithFields(fields map[string]interface{}) LogEntry
	WithError(err error) LogEntry

	// GCP Cloud Logging specific methods
	WithTrace(traceID string) LogEntry
	WithSpanID(spanID string) LogEntry
	WithOperation(id, producer string) LogEntry
	WithSourceLocation(file string, line int, function string) LogEntry

	// Direct logging methods
	Debug(msg string)
	Info(msg string)
	Warn(msg string)
	Error(msg string)
	Critical(msg string)
}

// LogEntry interface for chained logging operations
type LogEntry interface {
	WithField(key string, value interface{}) LogEntry
	WithFields(fields map[string]interface{}) LogEntry
	WithError(err error) LogEntry

	// GCP Cloud Logging specific methods
	WithTrace(traceID string) LogEntry
	WithSpanID(spanID string) LogEntry
	WithOperation(id, producer string) LogEntry
	WithSourceLocation(file string, line int, function string) LogEntry

	Debug(msg string)
	Info(msg string)
	Warn(msg string)
	Error(msg string)
	Critical(msg string)
}

// ZerologLogger implements Logger interface using zerolog
type ZerologLogger struct {
	logger zerolog.Logger
}

// ZerologEntry implements LogEntry interface
type ZerologEntry struct {
	logger zerolog.Logger
}

// NewZerologLogger creates a new zerolog-based logger with GCP Cloud Logging compatibility
func NewZerologLogger(output io.Writer) Logger {
	// Configure zerolog for GCP Cloud Logging compatibility
	zerolog.TimeFieldFormat = time.RFC3339Nano

	// Check if running in GCP environment
	isGCP := os.Getenv("APP_ENV") == "production" || os.Getenv("K_SERVICE") != ""

	var logger zerolog.Logger

	if isGCP {
		// GCP Cloud Logging configuration
		logger = zerolog.New(output).With().
			Str("service", getServiceName()).
			Str("version", getServiceVersion()).
			Logger()

		// Configure GCP-specific field mappings
		zerolog.LevelFieldName = GCPSeverityField
		zerolog.MessageFieldName = GCPMessageField
		zerolog.TimestampFieldName = GCPTimestampField

		// Map zerolog levels to GCP severity levels
		zerolog.LevelFieldMarshalFunc = func(l zerolog.Level) string {
			switch l {
			case zerolog.DebugLevel:
				return SeverityDebug
			case zerolog.InfoLevel:
				return SeverityInfo
			case zerolog.WarnLevel:
				return SeverityWarning
			case zerolog.ErrorLevel:
				return SeverityError
			case zerolog.FatalLevel, zerolog.PanicLevel:
				return SeverityCritical
			default:
				return SeverityDefault
			}
		}
	} else {
		// Local development configuration
		logger = zerolog.New(output).With().Timestamp().Logger()
	}

	// Set log level from environment or default to Info
	level := os.Getenv("LOG_LEVEL")
	switch level {
	case "debug":
		logger = logger.Level(zerolog.DebugLevel)
	case "warn":
		logger = logger.Level(zerolog.WarnLevel)
	case "error":
		logger = logger.Level(zerolog.ErrorLevel)
	default:
		logger = logger.Level(zerolog.InfoLevel)
	}

	return &ZerologLogger{logger: logger}
}

// getServiceName returns the service name for GCP logging
func getServiceName() string {
	if name := os.Getenv("K_SERVICE"); name != "" {
		return name // Cloud Run service name
	}
	if name := os.Getenv("SERVICE_NAME"); name != "" {
		return name
	}
	return "izy-api" // Default service name
}

// getServiceVersion returns the service version for GCP logging
func getServiceVersion() string {
	if version := os.Getenv("K_REVISION"); version != "" {
		return version // Cloud Run revision
	}
	if version := os.Getenv("SERVICE_VERSION"); version != "" {
		return version
	}
	return "unknown"
}

// ZerologLogger implementation

// WithContext creates a new logger entry with context values
func (l *ZerologLogger) WithContext(ctx context.Context) LogEntry {
	// Create a logger context instead of an event
	logger := l.logger

	// Extract OrderID from context
	if orderID := ctx.Value(OrderIDKey); orderID != nil {
		logger = logger.With().Str("order_id", orderID.(string)).Logger()
	}

	// Extract UserID from context
	if userExternalID := ctx.Value(UserExternalIdKey); userExternalID != nil {
		logger = logger.With().Str("user_external_id", userExternalID.(string)).Logger()
	}

	// Extract CompanyID from context
	if companyExternalID := ctx.Value(CompanyExternalIdKey); companyExternalID != nil {
		logger = logger.With().Str("company_external_id", companyExternalID.(string)).Logger()
	}

	return &ZerologEntry{logger: logger}
}

// WithOrderID creates a logger entry with OrderID
func (l *ZerologLogger) WithOrderID(orderID string) LogEntry {
	return &ZerologEntry{logger: l.logger.With().Str("order_id", orderID).Logger()}
}

// WithUserID creates a logger entry with UserID
func (l *ZerologLogger) WithUserExternalID(userExternalID string) LogEntry {
	return &ZerologEntry{logger: l.logger.With().Str("user_external_id", userExternalID).Logger()}
}

// WithCompanyID creates a logger entry with CompanyID
func (l *ZerologLogger) WithCompanyExternalID(companyExternalID string) LogEntry {
	return &ZerologEntry{logger: l.logger.With().Str("company_id", companyExternalID).Logger()}
}

// WithFields creates a logger entry with custom fields
func (l *ZerologLogger) WithFields(fields map[string]interface{}) LogEntry {
	ctx := l.logger.With()
	for k, v := range fields {
		ctx = ctx.Interface(k, v)
	}
	return &ZerologEntry{logger: ctx.Logger()}
}

// WithError creates a logger entry with error
func (l *ZerologLogger) WithError(err error) LogEntry {
	return &ZerologEntry{logger: l.logger.With().Err(err).Logger()}
}

// GCP Cloud Logging specific methods for ZerologLogger
func (l *ZerologLogger) WithTrace(traceID string) LogEntry {
	return &ZerologEntry{logger: l.logger.With().Str(GCPTraceField, traceID).Logger()}
}

func (l *ZerologLogger) WithSpanID(spanID string) LogEntry {
	return &ZerologEntry{logger: l.logger.With().Str(GCPSpanIDField, spanID).Logger()}
}

func (l *ZerologLogger) WithOperation(id, producer string) LogEntry {
	operation := map[string]string{
		"id":       id,
		"producer": producer,
	}
	return &ZerologEntry{logger: l.logger.With().Interface(GCPOperationField, operation).Logger()}
}

func (l *ZerologLogger) WithSourceLocation(file string, line int, function string) LogEntry {
	sourceLocation := map[string]interface{}{
		"file":     file,
		"line":     line,
		"function": function,
	}
	return &ZerologEntry{logger: l.logger.With().Interface(GCPSourceLocationField, sourceLocation).Logger()}
}

// Direct logging methods for ZerologLogger
func (l *ZerologLogger) Debug(msg string) {
	l.logger.Debug().Msg(msg)
}

func (l *ZerologLogger) Info(msg string) {
	l.logger.Info().Msg(msg)
}

func (l *ZerologLogger) Warn(msg string) {
	l.logger.Warn().Msg(msg)
}

func (l *ZerologLogger) Error(msg string) {
	l.logger.Error().Msg(msg)
}

func (l *ZerologLogger) Critical(msg string) {
	l.logger.Fatal().Msg(msg)
}

// ZerologEntry implementation

// WithField adds a field to the log entry
func (e *ZerologEntry) WithField(key string, value interface{}) LogEntry {
	return &ZerologEntry{logger: e.logger.With().Interface(key, value).Logger()}
}

// WithFields adds multiple fields to the log entry
func (e *ZerologEntry) WithFields(fields map[string]interface{}) LogEntry {
	ctx := e.logger.With()
	for k, v := range fields {
		ctx = ctx.Interface(k, v)
	}
	return &ZerologEntry{logger: ctx.Logger()}
}

// WithError adds an error to the log entry
func (e *ZerologEntry) WithError(err error) LogEntry {
	return &ZerologEntry{logger: e.logger.With().Err(err).Logger()}
}

// GCP Cloud Logging specific methods for ZerologEntry
func (e *ZerologEntry) WithTrace(traceID string) LogEntry {
	return &ZerologEntry{logger: e.logger.With().Str(GCPTraceField, traceID).Logger()}
}

func (e *ZerologEntry) WithSpanID(spanID string) LogEntry {
	return &ZerologEntry{logger: e.logger.With().Str(GCPSpanIDField, spanID).Logger()}
}

func (e *ZerologEntry) WithOperation(id, producer string) LogEntry {
	operation := map[string]string{
		"id":       id,
		"producer": producer,
	}
	return &ZerologEntry{logger: e.logger.With().Interface(GCPOperationField, operation).Logger()}
}

func (e *ZerologEntry) WithSourceLocation(file string, line int, function string) LogEntry {
	sourceLocation := map[string]interface{}{
		"file":     file,
		"line":     line,
		"function": function,
	}
	return &ZerologEntry{logger: e.logger.With().Interface(GCPSourceLocationField, sourceLocation).Logger()}
}

// Logging methods for ZerologEntry
func (e *ZerologEntry) Debug(msg string) {
	e.logger.Debug().Msg(msg)
}

func (e *ZerologEntry) Info(msg string) {
	e.logger.Info().Msg(msg)
}

func (e *ZerologEntry) Warn(msg string) {
	e.logger.Warn().Msg(msg)
}

func (e *ZerologEntry) Error(msg string) {
	e.logger.Error().Msg(msg)
}

func (e *ZerologEntry) Critical(msg string) {
	e.logger.Fatal().Msg(msg)
}

// Global logger instance
var defaultLogger Logger = NewZerologLogger(os.Stdout)

// GetLogger returns the default logger instance
func GetLogger() Logger {
	return defaultLogger
}

// Context helper functions

// WithOrderID adds OrderID to context
func WithOrderID(ctx context.Context, orderID string) context.Context {
	return context.WithValue(ctx, OrderIDKey, orderID)
}

// WithUserID adds UserID to context
func WithUserID(ctx context.Context, userExternalID string) context.Context {
	return context.WithValue(ctx, UserExternalIdKey, userExternalID)
}

// WithCompanyID adds CompanyID to context
func WithCompanyExternalID(ctx context.Context, companyExternalID string) context.Context {
	return context.WithValue(ctx, CompanyExternalIdKey, companyExternalID)
}

// GetOrderID extracts OrderID from context
func GetOrderID(ctx context.Context) (string, bool) {
	orderID, ok := ctx.Value(OrderIDKey).(string)
	return orderID, ok
}

// GetUserID extracts UserID from context
func GetUserID(ctx context.Context) (string, bool) {
	userExternalID, ok := ctx.Value(UserExternalIdKey).(string)
	return userExternalID, ok
}

// GetCompanyID extracts CompanyID from context
func GetCompanyExternalID(ctx context.Context) (string, bool) {
	companyExternalID, ok := ctx.Value(CompanyExternalIdKey).(string)
	return companyExternalID, ok
}

// Convenience functions for common logging patterns

// LogCheckoutStep logs a checkout step with context
func LogCheckoutStep(ctx context.Context, step string, details map[string]interface{}) {
	entry := defaultLogger.WithContext(ctx).WithField("step", step)
	if details != nil {
		entry = entry.WithFields(details)
	}
	entry.Info("Checkout step executed")
}

// LogCouponValidation logs coupon validation with context
func LogCouponValidation(ctx context.Context, couponCode string, result string, details map[string]interface{}) {
	entry := defaultLogger.WithContext(ctx).WithFields(map[string]interface{}{
		"coupon_code": couponCode,
		"result":      result,
	})
	if details != nil {
		entry = entry.WithFields(details)
	}
	entry.Info("Coupon validation completed")
}

// LogPaymentCreation logs payment creation with context
func LogPaymentCreation(ctx context.Context, paymentMethod string, amount int32, details map[string]interface{}) {
	entry := defaultLogger.WithContext(ctx).WithFields(map[string]interface{}{
		"payment_method": paymentMethod,
		"amount":         amount,
	})
	if details != nil {
		entry = entry.WithFields(details)
	}
	entry.Info("Payment creation initiated")
}

// LogError logs an error with context
func LogError(ctx context.Context, err error, message string, details map[string]interface{}) {
	entry := defaultLogger.WithContext(ctx).WithError(err)
	if details != nil {
		entry = entry.WithFields(details)
	}
	entry.Error(message)
}

// GCP Cloud Logging specific convenience functions

// LogWithTrace logs a message with GCP trace information
func LogWithTrace(ctx context.Context, traceID string, level string, message string, details map[string]interface{}) {
	entry := defaultLogger.WithContext(ctx).WithTrace(traceID)
	if details != nil {
		entry = entry.WithFields(details)
	}

	switch level {
	case SeverityDebug:
		entry.Debug(message)
	case SeverityInfo:
		entry.Info(message)
	case SeverityWarning:
		entry.Warn(message)
	case SeverityError:
		entry.Error(message)
	case SeverityCritical:
		entry.Critical(message)
	default:
		entry.Info(message)
	}
}

// LogHTTPRequest logs HTTP request information in GCP format
func LogHTTPRequest(ctx context.Context, method, url, userAgent string, statusCode int, latency time.Duration, details map[string]interface{}) {
	httpRequest := map[string]interface{}{
		"requestMethod": method,
		"requestUrl":    url,
		"userAgent":     userAgent,
		"status":        statusCode,
		"latency":       latency.String(),
	}

	entry := defaultLogger.WithContext(ctx).WithField("httpRequest", httpRequest)
	if details != nil {
		entry = entry.WithFields(details)
	}

	if statusCode >= 400 {
		entry.Error("HTTP request completed with error")
	} else {
		entry.Info("HTTP request completed")
	}
}

// LogBusinessEvent logs business events with structured data for GCP
func LogBusinessEvent(ctx context.Context, eventType, eventName string, details map[string]interface{}) {
	event := map[string]interface{}{
		"type": eventType,
		"name": eventName,
	}
	if details != nil {
		event["details"] = details
	}

	defaultLogger.WithContext(ctx).
		WithField("businessEvent", event).
		Info("Business event occurred")
}

// ExtractTraceFromRequest extracts trace ID from HTTP request headers (GCP format)
func ExtractTraceFromRequest(traceHeader string) string {
	// GCP trace header format: "X-Cloud-Trace-Context: TRACE_ID/SPAN_ID;o=TRACE_TRUE"
	if traceHeader == "" {
		return ""
	}

	// Extract just the trace ID part
	parts := strings.Split(traceHeader, "/")
	if len(parts) > 0 {
		return parts[0]
	}

	return ""
}
