// Package withdrawalprocessor provides a Cloud Function for processing withdrawal payouts and refreshing balance views.
package withdrawalprocessor

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/GoogleCloudPlatform/functions-framework-go/functions"
	"github.com/cloudevents/sdk-go/v2/event"
	"github.com/jackc/pgx/v4"

	"github.com/izy-mercado/withdrawal-processor/logger"
	"github.com/izy-mercado/withdrawal-processor/pkg/database"
)

// PayoutProcessorRequest represents the Cloud Scheduler payload
type PayoutProcessorRequest struct {
	Action string `json:"action"` // "update_payouts" or "refresh_balance_view"
}

// PubSubMessage represents the Pub/Sub message structure
type PubSubMessage struct {
	Message struct {
		Data      string `json:"data"` // base64 encoded payload
		MessageID string `json:"messageId"`
	} `json:"message"`
	Subscription string `json:"subscription"`
}

// PayoutProcessorResponse represents the function response
type PayoutProcessorResponse struct {
	Success       bool   `json:"success"`
	UpdatedCount  int    `json:"updated_count,omitempty"`
	Message       string `json:"message"`
	ExecutionTime string `json:"execution_time"`
	Timestamp     string `json:"timestamp"`
}

func init() {
	functions.CloudEvent("ProcessWithdrawalPayouts", processWithdrawalPayouts)
}

// processWithdrawalPayouts is the Cloud Function entry point for Pub/Sub events
func processWithdrawalPayouts(ctx context.Context, e event.Event) error {
	start := time.Now()

	logger := logger.GetLogger()

	logger.WithContext(ctx).Info(fmt.Sprintf("Processing Pub/Sub event: ID=%s, Type=%s, Source=%s, Data=%s", e.ID(), e.Type(), e.Source(), string(e.Data())))

	// Parse the Pub/Sub message structure
	var pubsubMsg PubSubMessage
	if err := json.Unmarshal(e.Data(), &pubsubMsg); err != nil {
		logger.WithContext(ctx).WithError(err).Error("Failed to unmarshal Pub/Sub message structure")
		return fmt.Errorf("invalid Pub/Sub message structure: %w", err)
	}

	// Decode the base64 encoded message data
	decodedData, err := base64.StdEncoding.DecodeString(pubsubMsg.Message.Data)
	if err != nil {
		logger.WithContext(ctx).WithError(err).Error("Failed to decode base64 message data")
		return fmt.Errorf("invalid base64 message data: %w", err)
	}

	logger.WithContext(ctx).Info(fmt.Sprintf("Decoded message data: %s", string(decodedData)))

	// Parse the actual payload
	var req PayoutProcessorRequest
	if err := json.Unmarshal(decodedData, &req); err != nil {
		logger.WithContext(ctx).WithError(err).Error("Failed to unmarshal payload")
		return fmt.Errorf("invalid payload format: %w", err)
	}

	// Validate action
	if req.Action != "update_payouts" && req.Action != "refresh_balance_view" {
		logger.WithContext(ctx).WithFields(map[string]interface{}{"action": req.Action}).Error("Invalid action")
		return fmt.Errorf("invalid action '%s': must be 'update_payouts' or 'refresh_balance_view'", req.Action)
	}

	logger.WithContext(ctx).WithFields(map[string]interface{}{"action": req.Action}).Info("Processing action")

	// Connect to database
	db, err := connectToDatabase()
	if err != nil {
		logger.WithContext(ctx).WithError(err).Error("Database connection failed")
		return fmt.Errorf("database connection failed: %w", err)
	}
	defer db.Close(ctx)

	var response PayoutProcessorResponse

	switch req.Action {
	case "update_payouts":
		response = updatePayoutStatuses(ctx, db)
	case "refresh_balance_view":
		response = refreshBalanceView(ctx, db)
	}

	// Add execution metadata
	response.ExecutionTime = time.Since(start).String()
	response.Timestamp = time.Now().UTC().Format(time.RFC3339)

	// Log the result
	if response.Success {
		logger.WithContext(ctx).WithFields(map[string]interface{}{"action": req.Action, "execution_time": response.ExecutionTime}).Info("Action completed successfully")
	} else {
		logger.WithContext(ctx).WithFields(map[string]interface{}{"action": req.Action, "message": response.Message}).Error("Action failed")
		return fmt.Errorf("action '%s' failed: %s", req.Action, response.Message)
	}

	logger.WithContext(ctx).WithFields(map[string]interface{}{"action": req.Action, "execution_time": response.ExecutionTime}).Info("Pub/Sub message processed successfully")
	return nil
}

// connectToDatabase establishes database connection using environment variables
func connectToDatabase() (*pgx.Conn, error) {
	// Get database configuration from environment variables
	databaseURL := os.Getenv("DATABASE_URL")

	// Validate required environment variables
	if databaseURL == "" {
		return nil, fmt.Errorf("missing required database environment variables")
	}

	// Parse the database URL
	conf, err := pgx.ParseConfig(databaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database URL: %w", err)
	}

	// Configure SSL if enabled using shared package
	sslConfig := database.LoadSSLConfigFromEnv()
	if sslConfig != nil && sslConfig.IsSSLEnabled() {
		tlsConfig, err := sslConfig.CreateTLSConfig()
		if err != nil {
			return nil, fmt.Errorf("failed to create TLS config: %w", err)
		}
		conf.TLSConfig = tlsConfig
	}

	// Create connection with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Connect to database (single connection, no pool needed for Cloud Functions)
	conn, err := pgx.ConnectConfig(ctx, conf)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Test connection
	if err := conn.Ping(ctx); err != nil {
		conn.Close(ctx)
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return conn, nil
}

// updatePayoutStatuses updates pending payouts to available status
func updatePayoutStatuses(ctx context.Context, db *pgx.Conn) PayoutProcessorResponse {
	query := `
		UPDATE invoice_payouts
		SET status = 'available', updated_at = NOW()
		WHERE status = 'pending'
		  AND available_after <= NOW()
	`

	result, err := db.Exec(ctx, query)
	if err != nil {
		return PayoutProcessorResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to update payout statuses: %v", err),
		}
	}

	rowsAffected := result.RowsAffected()

	return PayoutProcessorResponse{
		Success:      true,
		UpdatedCount: int(rowsAffected),
		Message:      fmt.Sprintf("Successfully updated %d payouts from pending to available", rowsAffected),
	}
}

// refreshBalanceView refreshes the materialized view for fast balance calculations
func refreshBalanceView(ctx context.Context, db *pgx.Conn) PayoutProcessorResponse {
	query := `REFRESH MATERIALIZED VIEW company_available_balances`

	_, err := db.Exec(ctx, query)
	if err != nil {
		return PayoutProcessorResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to refresh balance view: %v", err),
		}
	}

	return PayoutProcessorResponse{
		Success: true,
		Message: "Successfully refreshed company available balances materialized view",
	}
}
