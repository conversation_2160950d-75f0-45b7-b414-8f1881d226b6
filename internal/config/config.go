package config

import "github.com/vrischmann/envconfig"

type Core struct {
	BASIC_AUTH_PASSWORD string `envconfig:"BASIC_AUTH_PASSWORD"`
	JWT_SECRET          string `envconfig:"JWT_SECRET"`
	DATABASE_URL        string `envconfig:"DATABASE_URL"`
	SWAGGER_HOST        string `envconfig:"SWAGGER_HOST"`
}

type Woovi struct {
	API_KEY    string `envconfig:"WOOVI_API_KEY"`
	URL        string `envconfig:"WOOVI_URL"`
	PIX_KEY    string `envconfig:"WOOVI_PIX_KEY"`
	WEBHOOK_KEY string `envconfig:"WOOVI_WEBHOOK_KEY"`
}

type Storage struct {
	BUCKET_NAME       string `envconfig:"STORAGE_BUCKET_NAME"`
	ACCOUNT_ID        string `envconfig:"STORAGE_ACCOUNT_ID"`
	ACCESS_KEY_ID     string `envconfig:"STORAGE_ACCESS_KEY_ID"`
	ACCESS_KEY_SECRET string `envconfig:"STORAGE_ACCESS_KEY_SECRET"`
}
type Mailer struct {
	FROM                   string `envconfig:"MAILER_FROM"`
	TOKEN                  string `envconfig:"MAILER_TOKEN"`
	URL                    string `envconfig:"MAILER_URL"`
	SEND_CODE_TEMPLATE_KEY string `envconfig:"MAILER_SEND_CODE_TEMPLATE_KEY"`
}

type NewRelic struct {
	LICENSE_KEY string `envconfig:"NEW_RELIC_LICENSE_KEY"`
	APP_NAME    string `envconfig:"NEW_RELIC_APP_NAME"`
}

type FCM struct {
	ServiceAccountPath string `envconfig:"FCM_SERVICE_ACCOUNT_PATH,optional"` // For local development
	ServiceAccountJSON string `envconfig:"FCM_SERVICE_ACCOUNT_JSON,optional"` // For production (JSON content)
	ProjectID          string `envconfig:"FCM_PROJECT_ID"`
	VAPIDKey           string `envconfig:"FCM_VAPID_KEY"`
}

type Environment struct {
	Core     Core
	Woovi    Woovi
	Storage  Storage
	Mailer   Mailer
	NewRelic NewRelic
	FCM      FCM
}

func Must() *Environment {
	var env Environment
	if err := envconfig.Init(&env); err != nil {
		panic(err)
	}
	return &env
}
