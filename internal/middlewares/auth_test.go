package middlewares_test

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/middlewares"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
)

func mockDB() *postgres.Queries {
	return &postgres.Queries{}
}

func TestValidateToken(t *testing.T) {
	m := middlewares.New(&config.Environment{}, mockDB())
	scenarios := map[string]struct {
		token string
		want  string
	}{
		"invalid token": {
			token: "Bearer 1234fsedfsdfsdfsdfsdf",
			want:  "invalid token",
		},
		"missing token": {
			token: "dasdasdasdas",
			want:  "missing token",
		},
	}

	req, _ := http.NewRequest("GET", "/", nil)

	for name, scenario := range scenarios {
		t.Run(name, func(t *testing.T) {
			req.Header.Set("Authorization", scenario.token)
			_, _, err := m.ValidateToken(req)
			if err.Error() != scenario.want {
				t.Errorf("ValidateToken() = %v, want %v", err.Error(), scenario.want)
			}
		})
	}

}

func TestWebhookAuthentication(t *testing.T) {
	env := &config.Environment{
		Woovi: config.Woovi{
			WEBHOOK_KEY: "test-webhook-key",
		},
	}
	m := middlewares.New(env, mockDB())

	scenarios := map[string]struct {
		authHeader     string
		expectedStatus int
		description    string
	}{
		"valid_bearer_token": {
			authHeader:     "Bearer test-webhook-key",
			expectedStatus: http.StatusOK,
			description:    "Valid bearer token should pass",
		},
		"valid_direct_key": {
			authHeader:     "test-webhook-key",
			expectedStatus: http.StatusOK,
			description:    "Valid direct API key should pass",
		},
		"invalid_key": {
			authHeader:     "Bearer wrong-key",
			expectedStatus: http.StatusUnauthorized,
			description:    "Invalid API key should fail",
		},
		"missing_auth": {
			authHeader:     "",
			expectedStatus: http.StatusUnauthorized,
			description:    "Missing authorization should fail",
		},
		"empty_bearer": {
			authHeader:     "Bearer ",
			expectedStatus: http.StatusUnauthorized,
			description:    "Empty bearer token should fail",
		},
	}

	for name, scenario := range scenarios {
		t.Run(name, func(t *testing.T) {
			// Create a test handler that returns 200 OK if reached
			testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
			})

			// Wrap the test handler with the webhook authentication middleware
			handler := m.WebhookAuthentication(testHandler)

			// Create request
			req, _ := http.NewRequest("POST", "/webhook", nil)
			if scenario.authHeader != "" {
				req.Header.Set("Authorization", scenario.authHeader)
			}

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the request
			handler.ServeHTTP(rr, req)

			// Check the status code
			if rr.Code != scenario.expectedStatus {
				t.Errorf("%s: expected status %d, got %d", scenario.description, scenario.expectedStatus, rr.Code)
			}
		})
	}
}
