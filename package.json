{"name": "backend", "version": "1.0.0", "description": "[![Deploy from Release](https://github.com/Izy-Mercado/backend/actions/workflows/deploy.production.yml/badge.svg)](https://github.com/Izy-Mercado/backend/actions/workflows/deploy.production.yml)", "main": "playwright.js", "type": "module", "directories": {"doc": "docs"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Izy-Mercado/backend.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Izy-Mercado/backend/issues"}, "homepage": "https://github.com/Izy-Mercado/backend#readme", "dependencies": {"playwright": "^1.54.2"}}