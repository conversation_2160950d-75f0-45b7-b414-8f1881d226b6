-- Add original_quantity column to coupons table
ALTER TABLE coupons ADD COLUMN original_quantity INTEGER;

-- Update existing records to set original_quantity = quantity + used_count
UPDATE coupons 
SET original_quantity = quantity + COALESCE(usage_stats.used_count, 0)
FROM (
    SELECT 
        coupon_id,
        COUNT(*) AS used_count
    FROM users_coupons
    GROUP BY coupon_id
) usage_stats
WHERE coupons.id = usage_stats.coupon_id;

-- For coupons that have never been used, set original_quantity = quantity
UPDATE coupons 
SET original_quantity = quantity 
WHERE original_quantity IS NULL;

-- Make the column NOT NULL after setting all values
ALTER TABLE coupons ALTER COLUMN original_quantity SET NOT NULL;

-- Add check constraint to ensure original_quantity >= quantity
ALTER TABLE coupons ADD CONSTRAINT coupons_original_quantity_check 
CHECK (original_quantity >= quantity);

-- Add comment for documentation
COMMENT ON COLUMN coupons.original_quantity IS 'Original quantity of coupons created (never changes after creation)';
