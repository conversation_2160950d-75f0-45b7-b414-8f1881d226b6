import { chromium } from 'playwright';
import path from 'path';
import os from 'os';
import fs from 'fs';

const GROUP_NAME = 'Digai - Mandem Oi';
const WA_LINK_REGEX = /https:\/\/wa\.me\/\d+\?text=[^"'\s]+/i;

const userDataDir = path.join(os.homedir(), '.whatsapp-session');
if (!fs.existsSync(userDataDir)) {
  fs.mkdirSync(userDataDir, { recursive: true });
}

(async () => {
  const context = await chromium.launchPersistentContext(userDataDir, {
    headless: false,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage'
    ],
    viewport: { width: 1280, height: 800 }
  });

  const page = await context.newPage();
  await page.goto('https://web.whatsapp.com/');
  console.log('Escaneie o QR se necessário...');

  await page.waitForSelector('div[role="grid"]');
  await page.click(`text="${GROUP_NAME}"`);
  console.log('Grupo aberto. Monitorando...');

  const seenLinks = new Set();

  while (true) {
    const linkElements = await page.$$(`a[href^="https://wa.me/"]`);
    for (const linkEl of linkElements) {
      const href = await linkEl.getAttribute('href');
      if (href && WA_LINK_REGEX.test(href) && !seenLinks.has(href)) {
        seenLinks.add(href);
        console.log(`Novo link detectado: ${href}`);
        await linkEl.click();
        console.log('Link clicado.');

        // Espera abrir a conversa
        await page.waitForSelector('div[role="textbox"]', { timeout: 10000 });
        // Envia "oi"
        await page.type('div[role="textbox"]', 'oi');
        await page.keyboard.press('Enter');
        console.log('Mensagem "oi" enviada.');

        // Aqui poderia navegar para salvar contato,
        // mas esse fluxo exige mapear o menu "Informações de contato"
        // e preencher os campos — parte mais frágil da automação.
        return;
      }
    }
    await page.waitForTimeout(2000);
  }
})();
