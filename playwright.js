import { chromium } from 'playwright';
import path from 'path';
import os from 'os';
import fs from 'fs';

const GROUP_NAME = 'NOME_DO_GRUPO_AQUI';
const WA_LINK_REGEX = /https:\/\/wa\.me\/\d+\?text=[^"'\s]+/i;

const userDataDir = path.join(os.homedir(), '.whatsapp-session');
if (!fs.existsSync(userDataDir)) {
  fs.mkdirSync(userDataDir, { recursive: true });
}

(async () => {
  const context = await chromium.launchPersistentContext(userDataDir, {
    headless: false,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage'
    ],
    viewport: { width: 1280, height: 800 }
  });

  const page = await context.newPage();
  await page.goto('https://web.whatsapp.com/');
  console.log('Escaneie o QR se necessário...');

  await page.waitForSelector('div[role="grid"]');
  await page.click(`text="${GROUP_NAME}"`);
  console.log('Grupo aberto. Monitorando...');

  // Injeta um observador no DOM do navegador para capturar mensagens novas
  await page.exposeBinding('onNewWaLink', async (source, href) => {
    console.log(`Novo link detectado: ${href}`);
    const linkHandle = await page.$(`a[href="${href}"]`);
    if (linkHandle) {
      await linkHandle.click();
      console.log('Link clicado.');
      await page.waitForSelector('div[role="textbox"]', { timeout: 10000 });
      await page.type('div[role="textbox"]', 'oi');
      await page.keyboard.press('Enter');
      console.log('Mensagem "oi" enviada.');
      // Aqui poderia entrar fluxo para salvar contato
    }
  });

  await page.evaluate((regexSource) => {
    const regex = new RegExp(regexSource, 'i');
    const seen = new Set();

    const checkNode = (node) => {
      if (node.nodeType === Node.ELEMENT_NODE) {
        const links = node.querySelectorAll('a[href]');
        for (const link of links) {
          const href = link.getAttribute('href');
          if (href && regex.test(href) && !seen.has(href)) {
            seen.add(href);
            window.onNewWaLink(href);
          }
        }
      }
    };

    // Observa novas mensagens
    const observer = new MutationObserver((mutations) => {
      for (const m of mutations) {
        for (const node of m.addedNodes) {
          checkNode(node);
        }
      }
    });

    observer.observe(document.body, { childList: true, subtree: true });

    // Checa mensagens já carregadas
    document.querySelectorAll('a[href]').forEach(link => {
      const href = link.getAttribute('href');
      if (href && regex.test(href) && !seen.has(href)) {
        seen.add(href);
        window.onNewWaLink(href);
      }
    });
  }, WA_LINK_REGEX.source);

})();
