#!/bin/bash

echo "=== Database Configuration Debug ==="
echo

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ DATABASE_URL is not set"
    exit 1
else
    echo "✅ DATABASE_URL is set"
    
    # Extract components without showing the password
    DB_HOST=$(echo $DATABASE_URL | sed -n 's|.*://[^:]*:[^@]*@\([^:]*\):.*|\1|p')
    DB_PORT=$(echo $DATABASE_URL | sed -n 's|.*://[^:]*:[^@]*@[^:]*:\([0-9]*\)/.*|\1|p')
    DB_USER=$(echo $DATABASE_URL | sed -n 's|.*://\([^:]*\):.*|\1|p')
    DB_NAME=$(echo $DATABASE_URL | awk -F'/' '{print $NF}' | awk -F'?' '{print $1}')
    
    echo "  Host: $DB_HOST"
    echo "  Port: $DB_PORT"
    echo "  User: $DB_USER"
    echo "  Database: $DB_NAME"
    echo "  Password: [HIDDEN - $(echo $DATABASE_URL | sed -n 's|.*://[^:]*:\([^@]*\)@.*|\1|p' | wc -c) characters]"
    
    # Check for SSL parameters in URL
    if [[ "$DATABASE_URL" == *"sslmode="* ]]; then
        SSL_MODE=$(echo $DATABASE_URL | sed -n 's|.*sslmode=\([^&]*\).*|\1|p')
        echo "  SSL Mode in URL: $SSL_MODE"
    else
        echo "  SSL Mode in URL: not specified"
    fi
fi

echo

# Check SSL certificate environment variables
echo "=== SSL Certificate Configuration ==="
if [ -n "$DATABASE_SSL_SERVER_CA" ]; then
    echo "✅ DATABASE_SSL_SERVER_CA is set ($(echo "$DATABASE_SSL_SERVER_CA" | wc -c) characters)"
else
    echo "❌ DATABASE_SSL_SERVER_CA is not set"
fi

if [ -n "$DATABASE_SSL_CLIENT_CERT" ]; then
    echo "✅ DATABASE_SSL_CLIENT_CERT is set ($(echo "$DATABASE_SSL_CLIENT_CERT" | wc -c) characters)"
else
    echo "❌ DATABASE_SSL_CLIENT_CERT is not set"
fi

if [ -n "$DATABASE_SSL_CLIENT_KEY" ]; then
    echo "✅ DATABASE_SSL_CLIENT_KEY is set ($(echo "$DATABASE_SSL_CLIENT_KEY" | wc -c) characters)"
else
    echo "❌ DATABASE_SSL_CLIENT_KEY is not set"
fi

echo

# Test basic connectivity (without authentication)
echo "=== Basic Connectivity Test ==="
echo "Testing connection to $DB_HOST:$DB_PORT..."

if command -v nc &> /dev/null; then
    if nc -z -w5 "$DB_HOST" "$DB_PORT" 2>/dev/null; then
        echo "✅ Can reach $DB_HOST:$DB_PORT"
    else
        echo "❌ Cannot reach $DB_HOST:$DB_PORT"
    fi
else
    echo "⚠️  nc (netcat) not available, skipping connectivity test"
fi

echo
echo "=== Recommendations ==="
echo "1. Verify the password in DATABASE_URL is correct"
echo "2. Check if the database requires both password AND client certificate authentication"
echo "3. Ensure the database user 'postgres' exists and has the correct permissions"
echo "4. If using client certificates, verify they are valid and properly configured on the database server"
