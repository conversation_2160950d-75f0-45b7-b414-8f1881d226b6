import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Loader2, DollarSign, AlertCircle } from "lucide-react";
import { companyService } from "@/services/api";

import { formatCurrency } from "@/utils/formatters";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

interface WithdrawalModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  companyId?: string; // Optional for backward compatibility
  onWithdrawalSuccess?: () => void; // Optional callback for successful withdrawal
}

const WithdrawalModal: React.FC<WithdrawalModalProps> = ({ open, onOpenChange, companyId, onWithdrawalSuccess }) => {
  const queryClient = useQueryClient();

  // Fetch balance data
  const { data: balanceResponse, isLoading: isLoadingBalance, error: balanceError } = useQuery({
    queryKey: ["company-balance", companyId],
    queryFn: async () => {
      if (!companyId) {
        throw new Error("Company ID is required for balance data");
      }
      const response = await companyService.getBalance(companyId);
      return response;
    },
    enabled: open && !!companyId, // Only fetch when modal is open and companyId is available
    staleTime: 30000, // 30 seconds
  });

  // Withdrawal mutation
  const withdrawalMutation = useMutation({
    mutationFn: async (amount: number) => {
      if (!companyId) {
        throw new Error("Company ID is required for withdrawal");
      }
      return await companyService.withdraw({
        amount,
        company_external_id: companyId
      });
    },
    onSuccess: (response) => {
      const successMessage = response.data?.message || "Saque realizado com sucesso! O valor será processado em até 24 horas.";
      toast(successMessage, {
        dismissible: true,
        duration: 5000,
      });
      // Invalidate balance and withdrawal history queries to refresh data
      // Invalidate all company-balance queries (both with and without companyId)
      queryClient.invalidateQueries({ queryKey: ["company-balance"] });
      // Invalidate all withdrawal-history queries (both with and without companyId)
      queryClient.invalidateQueries({ queryKey: ["withdrawal-history"] });
      // If we have a specific companyId, also invalidate those specific queries
      if (companyId) {
        queryClient.invalidateQueries({ queryKey: ["company-balance", companyId] });
        queryClient.invalidateQueries({ queryKey: ["withdrawal-history", companyId] });
      }
      // Call the success callback if provided
      if (onWithdrawalSuccess) {
        onWithdrawalSuccess();
      }
      handleCloseModal();
    },
    onError: (error: any) => {
      console.error("Withdrawal error:", error);
      let errorMessage = "Erro ao processar saque. Tente novamente.";

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.status === 400) {
        errorMessage = "Dados inválidos. Verifique o valor do saque.";
      } else if (error.response?.status === 403) {
        errorMessage = "Saldo insuficiente ou operação não autorizada.";
      } else if (error.response?.status >= 500) {
        errorMessage = "Erro no servidor. Tente novamente mais tarde.";
      }

      toast(errorMessage, {
        dismissible: true,
        duration: 5000,
      });
    },
  });

  // Handle modal close
  const handleCloseModal = () => {
    onOpenChange(false);
  };

  // Process balance data - now it's a single object for the specific company
  const balanceData = balanceResponse?.data;

  // Use the balance from the specific company data
  const balance = balanceData?.balance || 0;

  // Handle withdrawal - withdraw company-specific or total balance based on context
  const handleWithdrawAll = () => {
    if (balance >= 50000) {
      withdrawalMutation.mutate(balance);
    }
  };

  const partnerName = balanceData?.name || '';
  const pixKey = balanceData?.pix_key || '';
  const canWithdraw = balance >= 50000;



  // Loading state
  if (isLoadingBalance) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[425px] mx-4">
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-sm sm:text-base">Carregando dados do saldo...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Error state
  if (balanceError) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[425px] mx-4">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-sm sm:text-base">
              <AlertCircle className="h-5 w-5 text-red-500" />
              Erro ao Carregar Saldo
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              Não foi possível carregar os dados do saldo. Tente novamente mais tarde.
            </p>
          </div>
          <DialogFooter>
            <Button onClick={handleCloseModal} className="w-full sm:w-auto">Fechar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  // Company not found state
  if (companyId && !balanceData) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[425px] mx-4">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-sm sm:text-base">
              <AlertCircle className="h-5 w-5 text-yellow-500" />
              Empresa Não Encontrada
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              Não foi possível encontrar os dados de saldo para esta empresa.
            </p>
          </div>
          <DialogFooter>
            <Button onClick={handleCloseModal} className="w-full sm:w-auto">Fechar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] mx-4 max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            {companyId ? 'Sacar Saldo da Empresa' : 'Sacar Todo o Saldo'}
          </DialogTitle>
          <DialogDescription>
            {companyId
              ? 'Você está prestes a sacar o saldo disponível desta empresa.'
              : 'Você está prestes a sacar todo o seu saldo disponível.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Partner Info */}
          <div className="bg-gray-50 p-3 sm:p-4 rounded-lg space-y-2">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
              <span className="text-sm font-medium text-gray-600">Parceiro:</span>
              <span className="text-sm font-semibold break-words">{partnerName}</span>
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
              <span className="text-sm font-medium text-gray-600">Chave PIX:</span>
              <span className="text-sm font-mono break-all">{pixKey}</span>
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
              <span className="text-sm font-medium text-gray-600">Valor do Saque:</span>
              <span className="text-xl font-bold text-green-600">
                {formatCurrency(balance / 100)}
              </span>
            </div>
          </div>

          {!canWithdraw && (
            <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <p className="text-sm text-yellow-800">
                  Saldo insuficiente. Valor mínimo para saque: R$ 500,00
                </p>
              </div>
            </div>
          )}

          {canWithdraw && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Importante:</strong> O valor será transferido via PIX para a chave cadastrada.
                O processamento pode levar até 24 horas úteis.
              </p>
            </div>
          )}

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleCloseModal}
              className="w-full sm:w-auto order-2 sm:order-1"
            >
              Cancelar
            </Button>
            {canWithdraw && (
              <Button
                onClick={handleWithdrawAll}
                disabled={withdrawalMutation.isPending}
                className="w-full sm:w-auto order-1 sm:order-2"
              >
                {withdrawalMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {companyId ? 'Sacar Saldo' : 'Sacar Tudo'}
              </Button>
            )}
            {!canWithdraw && (
              <Button
                onClick={handleCloseModal}
                className="w-full sm:w-auto order-1 sm:order-2"
              >
                Fechar
              </Button>
            )}
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default WithdrawalModal;
