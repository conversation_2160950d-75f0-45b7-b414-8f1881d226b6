import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery, useMutation } from "@tanstack/react-query";
import { couponService } from "@/services/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tag, Trash2 } from "lucide-react";
import { format, parseISO } from "date-fns";
import { useToast } from "@/components/ui/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

const CouponDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  const { data, isLoading, isError } = useQuery({
    queryKey: ["coupon", id],
    queryFn: async () => {
      try {
        const response = await couponService.getCoupon(id!);
        return response.data;
      } catch (error: any) {
        toast({
          variant: "destructive",
          title: "Erro ao carregar cupom",
          description: error.response?.data?.message || "Tente novamente mais tarde",
        });
        throw error;
      }
    },
  });

  const deleteCoupon = useMutation({
    mutationFn: async () => {
      await couponService.deleteCoupon(id!);
    },
    onSuccess: () => {
      toast({
        title: "Cupom removido com sucesso",
        description: "O cupom foi removido permanentemente.",
      });
      navigate("/admin/coupons");
    },
    onError: (error: any) => {
      toast({
        variant: "destructive",
        title: "Erro ao remover cupom",
        description: error.response?.data?.message || "Tente novamente mais tarde",
      });
    },
  });

  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), "dd/MM/yyyy HH:mm:ss");
    } catch (error) {
      console.error("Erro ao formatar data:", error);
      return "Data inválida";
    }
  };

  const formatValue = (value: number, type: "percentage" | "fixed") => {
    if (type === "percentage") {
      return `${value}%`;
    }
    return `R$ ${(value / 100).toFixed(2)}`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold tracking-tight">Detalhes do Cupom</h2>
        </div>
        <Card>
          <CardContent className="py-8">
            <div className="text-center">Carregando detalhes do cupom...</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isError || !data) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold tracking-tight">Detalhes do Cupom</h2>
        </div>
        <Card>
          <CardContent className="py-8">
            <div className="text-center text-red-500">
              Erro ao carregar detalhes do cupom. Tente novamente.
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const coupon = data.data;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">Detalhes do Cupom</h2>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="destructive">
              <Trash2 className="mr-2" size={18} />
              Remover Cupom
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Remover Cupom</AlertDialogTitle>
              <AlertDialogDescription>
                Tem certeza que deseja remover este cupom? Esta ação não pode ser
                desfeita.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => deleteCoupon.mutate()}
                className="bg-red-500 hover:bg-red-600"
              >
                Remover
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            <div className="flex items-center">
              <Tag className="mr-2" size={20} />
              Informações do Cupom
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-1">Código</h3>
                <p>{coupon.code}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-1">Tipo</h3>
                <p>
                  {coupon.type === "percentage" ? "Porcentagem" : "Valor Fixo"}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-1">Valor</h3>
                <p>{formatValue(coupon.value, coupon.type)}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-1">Valor Mínimo</h3>
                <p>R$ {(coupon.min_order_value / 100).toFixed(2)}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-1">Uso</h3>
                <p>{coupon.used_count}/{coupon.original_quantity}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-1">Status</h3>
                <p>{coupon.is_active ? "Ativo" : "Inativo"}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-1">Quantidade Restante</h3>
                <p>{coupon.quantity}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-1">Quantidade Original</h3>
                <p>{coupon.original_quantity}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-1">Data de Expiração</h3>
                <p>{formatDate(coupon.expires_at)}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-1">Tipo de Proprietário</h3>
                <p>
                  {coupon.owner_type === "admin"
                    ? "Administrador"
                    : "Empresa"}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-1">Data de Criação</h3>
                <p>{formatDate(coupon.created_at)}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-1">Última Atualização</h3>
                <p>{formatDate(coupon.updated_at)}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CouponDetails; 