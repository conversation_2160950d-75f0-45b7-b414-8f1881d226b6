import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Tag, Building2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useCoupons } from "@/hooks/useCoupons";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils";
import { useUpdateCouponStatus } from "@/hooks/useUpdateCouponStatus";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Coupon } from "@/types/api";

// Component to display company information for company-owned coupons
const CompanyInfo: React.FC<{ coupon: Coupon }> = ({ coupon }) => {
  // Only render for company-owned coupons
  if (coupon.owner_type !== "company") {
    return null;
  }

  // Check if company information is available
  if (!coupon.company_name || !coupon.company_cnpj) {
    return null;
  }

  return (
    <Badge variant="secondary" className="text-xs">
      <Building2 className="h-3 w-3 mr-1" />
      {coupon.company_name} - {coupon.company_cnpj}
    </Badge>
  );
};

const Coupons = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchParams, setSearchParams] = useSearchParams();

  // Get initial values from URL params or use defaults
  const [page, setPage] = useState(() => {
    const pageParam = searchParams.get('page');
    return pageParam ? parseInt(pageParam, 10) : 1;
  });

  const [limit, setLimit] = useState(() => {
    const limitParam = searchParams.get('limit');
    return limitParam ? parseInt(limitParam, 10) : 10;
  });

  const [showInactive, setShowInactive] = useState(false);
  const { data, isLoading, error } = useCoupons(page, limit);
  const updateStatusMutation = useUpdateCouponStatus();

  // Sync URL params with state on mount
  useEffect(() => {
    const pageParam = searchParams.get('page');
    const limitParam = searchParams.get('limit');

    if (pageParam && parseInt(pageParam, 10) !== page) {
      setPage(parseInt(pageParam, 10));
    }
    if (limitParam && parseInt(limitParam, 10) !== limit) {
      setLimit(parseInt(limitParam, 10));
    }
  }, [searchParams, page, limit]);

  // Update URL when page or limit changes
  const updateURL = (newPage: number, newLimit: number) => {
    const params = new URLSearchParams();
    if (newPage > 1) params.set('page', newPage.toString());
    if (newLimit !== 10) params.set('limit', newLimit.toString());
    setSearchParams(params);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    updateURL(newPage, limit);
  };

  const handleLimitChange = (newLimit: string) => {
    const limitNum = parseInt(newLimit, 10);
    setLimit(limitNum);
    setPage(1); // Reset to first page when changing limit
    updateURL(1, limitNum);
  };

  const handleStatusChange = async (couponId: string, newStatus: boolean) => {
    try {
      await updateStatusMutation.mutateAsync({ couponId, status: newStatus });
      toast({
        title: "Sucesso",
        description: "Status do cupom atualizado com sucesso!",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao atualizar status do cupom",
        variant: "destructive",
      });
    }
  };

  // Use API response data for pagination
  const coupons = data?.data || [];
  // Note: Client-side filtering only works on current page since we use server-side pagination
  const filteredCoupons = coupons.filter(coupon => showInactive || coupon.is_active);
  const totalPages = data?.totalPages || 0;
  const totalItems = data?.totalItems || 0;

  if (error) return (
    <div className="flex items-center justify-center h-[50vh]">
      <Card className="w-full max-w-md">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <p className="text-lg font-medium text-destructive">Erro ao carregar cupons</p>
            <Button onClick={() => window.location.reload()}>Tentar novamente</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold">Cupons</h1>
          <p className="text-sm text-muted-foreground">
            Gerencie os cupons de desconto disponíveis
          </p>
        </div>
        <Button onClick={() => navigate("/admin/coupons/new")}>
          <Plus className="mr-2 h-4 w-4" />
          Novo Cupom
        </Button>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Switch
            id="show-inactive"
            checked={showInactive}
            onCheckedChange={setShowInactive}
          />
          <Label htmlFor="show-inactive">Mostrar cupons inativos</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Label htmlFor="page-size">Itens por página:</Label>
          <Select value={limit.toString()} onValueChange={handleLimitChange}>
            <SelectTrigger id="page-size" className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardContent className="p-6">
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="p-4">
                  <div className="space-y-3">
                    <Skeleton className="h-4 w-[250px]" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[200px]" />
                      <Skeleton className="h-4 w-[150px]" />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : filteredCoupons.length === 0 ? (
            <div className="text-center py-8">
              <Tag className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">Nenhum cupom encontrado</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                {showInactive 
                  ? "Não há cupons cadastrados no sistema."
                  : "Não há cupons ativos no momento."}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredCoupons.map((coupon) => (
                <Card key={coupon.external_id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg font-mono">{coupon.code}</CardTitle>
                        <div className="flex flex-wrap gap-2 mt-2">
                          <Badge variant={coupon.is_active ? "default" : "secondary"}>
                            {coupon.is_active ? "Ativo" : "Inativo"}
                          </Badge>
                          <Badge variant="outline">
                            {coupon.type === "percentage" ? "Porcentagem" : "Valor Fixo"}
                          </Badge>
                          {/* Company Information for company-owned coupons */}
                          <CompanyInfo coupon={coupon} />
                        </div>
                      </div>
                      <Button
                        variant={coupon.is_active ? "destructive" : "default"}
                        size="sm"
                        onClick={() => handleStatusChange(coupon.external_id, !coupon.is_active)}
                      >
                        {coupon.is_active ? "Desativar" : "Ativar"}
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Valor do Desconto</p>
                        <p className="font-medium">
                          {coupon.type === "percentage" 
                            ? `${coupon.value / 100}%` 
                            : formatCurrency(coupon.value / 100)}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Valor Mínimo</p>
                        <p className="font-medium">{formatCurrency(coupon.min_order_value / 100)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Uso</p>
                        <p className="font-medium">{coupon.used_count}/{coupon.original_quantity}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Expira em</p>
                        <p className="font-medium">
                          {new Date(coupon.expires_at).toLocaleDateString('pt-BR', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric'
                          })}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {!isLoading && totalPages > 1 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div>
              Mostrando {((page - 1) * limit) + 1} a {Math.min(page * limit, totalItems)} de {totalItems} cupons
              {!showInactive && (
                <span className="ml-1 text-xs">(apenas ativos nesta página)</span>
              )}
            </div>
            <div>
              Página {page} de {totalPages}
            </div>
          </div>

          <div className="flex justify-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => handlePageChange(Math.max(1, page - 1))}
                    className={page === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>

                {/* Show page numbers with smart truncation */}
                {(() => {
                  const pages = [];
                  const showPages = 5; // Show 5 page numbers at most
                  let startPage = Math.max(1, page - Math.floor(showPages / 2));
                  let endPage = Math.min(totalPages, startPage + showPages - 1);

                  // Adjust start if we're near the end
                  if (endPage - startPage < showPages - 1) {
                    startPage = Math.max(1, endPage - showPages + 1);
                  }

                  // Always show first page
                  if (startPage > 1) {
                    pages.push(
                      <PaginationItem key={1}>
                        <PaginationLink
                          onClick={() => handlePageChange(1)}
                          isActive={page === 1}
                          className="cursor-pointer"
                        >
                          1
                        </PaginationLink>
                      </PaginationItem>
                    );
                    if (startPage > 2) {
                      pages.push(
                        <PaginationItem key="start-ellipsis">
                          <span className="px-3 py-2">...</span>
                        </PaginationItem>
                      );
                    }
                  }

                  // Show middle pages
                  for (let i = startPage; i <= endPage; i++) {
                    pages.push(
                      <PaginationItem key={i}>
                        <PaginationLink
                          onClick={() => handlePageChange(i)}
                          isActive={page === i}
                          className="cursor-pointer"
                        >
                          {i}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  }

                  // Always show last page
                  if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                      pages.push(
                        <PaginationItem key="end-ellipsis">
                          <span className="px-3 py-2">...</span>
                        </PaginationItem>
                      );
                    }
                    pages.push(
                      <PaginationItem key={totalPages}>
                        <PaginationLink
                          onClick={() => handlePageChange(totalPages)}
                          isActive={page === totalPages}
                          className="cursor-pointer"
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  }

                  return pages;
                })()}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => handlePageChange(Math.min(totalPages, page + 1))}
                    className={page === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      )}
    </div>
  );
};

export default Coupons; 